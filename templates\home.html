{% extends "base.html" %}

{% block title %}首页 - 刷题网站{% endblock %}

{% block content %}
<div class="jumbotron fade-in">
    <div class="container">
        <h1 class="display-4 fw-bold">提升编程能力的智能刷题平台</h1>
        <p class="lead my-4">借助AI技术，为您提供个性化学习体验，让编程学习更高效、更有趣</p>
        <div class="d-flex gap-3 mt-4">
            <a class="btn btn-primary btn-lg" href="{{ url_for('problems') }}" role="button">
                <i class="fas fa-code me-2"></i>浏览题库
            </a>
            <a class="btn btn-outline-light btn-lg" href="{{ url_for('register') }}" role="button">
                <i class="fas fa-user-plus me-2"></i>立即注册
            </a>
        </div>
    </div>
</div>

<div class="container fade-in">
    <div class="row mt-5 mb-4">
        <div class="col-12 text-center">
            <h2 class="fw-bold">为什么选择我们的平台？</h2>
            <p class="text-muted">智能化学习体验，让刷题更高效</p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-robot fa-2x"></i>
                    </div>
                    <h4 class="card-title">AI智能搜索</h4>
                    <p class="card-text">使用自然语言描述，快速找到符合您需求的题目</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-lightbulb fa-2x"></i>
                    </div>
                    <h4 class="card-title">知识点解析</h4>
                    <p class="card-text">AI自动生成题目相关知识点，帮助您更好地理解和掌握</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                    <h4 class="card-title">视频解析</h4>
                    <p class="card-text">AI智能推荐相关视频教程，多维度学习解题思路</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-fire me-2 text-primary"></i>热门题目</h5>
                    <a href="{{ url_for('problems') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
                </div>
                <div class="card-body">
                    {% if problems %}
                    <div class="list-group list-group-flush">
                        {% for problem in problems %}
                        <a href="{{ url_for('problem_detail', problem_id=problem.id) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ problem.title }}</h6>
                                <small class="text-muted">{{ problem.category }}</small>
                            </div>
                            <span class="badge {% if problem.difficulty == '简单' %}bg-success{% elif problem.difficulty == '中等' %}bg-warning{% else %}bg-danger{% endif %} rounded-pill">
                                {{ problem.difficulty }}
                            </span>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-center py-3">暂无题目</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-search me-2 text-primary"></i>AI智能搜索</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('ai_search') }}" method="post">
                        <div class="input-group mb-3">
                            <span class="input-group-text bg-white"><i class="fas fa-robot text-primary"></i></span>
                            <input type="text" class="form-control" placeholder="例如：查找与二叉树相关的简单题目" name="query" required>
                            <button class="btn btn-primary" type="submit">搜索</button>
                        </div>
                    </form>
                    <p class="small text-muted text-center">使用自然语言描述您想要的题目类型，AI将为您智能匹配</p>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-star me-2 text-primary"></i>平台特色</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="feature-icon bg-light rounded me-3 p-2">
                                    <i class="fas fa-brain text-primary"></i>
                                </div>
                                <div>AI辅助搜索</div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="feature-icon bg-light rounded me-3 p-2">
                                    <i class="fas fa-lightbulb text-primary"></i>
                                </div>
                                <div>知识点生成</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="feature-icon bg-light rounded me-3 p-2">
                                    <i class="fas fa-video text-primary"></i>
                                </div>
                                <div>视频解析推荐</div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="feature-icon bg-light rounded me-3 p-2">
                                    <i class="fas fa-route text-primary"></i>
                                </div>
                                <div>个性化学习路径</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<form method="post" action="{{ url_for('set_api_key') }}" class="mb-3">
  <div class="row g-2 align-items-center">
    <div class="col-auto">
      <input type="text" name="api_key" class="form-control" placeholder="输入你的 API 密钥" required>
    </div>
    <div class="col-auto">
      <select class="form-select" id="ai_type" name="ai_type">
        <option value="openai">OpenAI</option>
        <option value="deepseek">DeepSeek</option>
        <option value="baidu">百度（未实现）</option>
        <option value="xfyun">讯飞（未实现）</option>
      </select>
    </div>
    <div class="col-auto">
      <button type="submit" class="btn btn-primary">保存密钥</button>
    </div>
  </div>
  <div class="form-text">API 密钥仅保存在本地会话，不会上传或泄露。</div>
</form>
{% endblock %}
