import requests

url = "https://api.deepseek.com/v1/chat/completions"
headers = {
    "Authorization": "Bearer sk-4899a185da0240de83434b320ae912b8",
    "Content-Type": "application/json"
}
data = {
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "你好，测试一下API"}],
    "temperature": 0.7,
    "max_tokens": 1000
}

try:
    response = requests.post(url, headers=headers, json=data, timeout=30)
    print(response.status_code)
    print(response.text)
except Exception as e:
    print("请求异常：", e) 