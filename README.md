# 刷题网站系统

这是一个使用Flask和Python实现的刷题网站系统，具有用户注册登录、题库搜索、AI知识点生成和视频解析搜索等功能。

## 功能特点

- 用户注册和登录
- 题库浏览和搜索
- AI辅助搜索题目
- AI生成题目知识点
- AI搜索视频解析
- 用户做题记录

## 技术栈

- 后端：Python + Flask
- 数据库：SQLite（通过SQLAlchemy ORM）
- 前端：HTML + CSS + JavaScript + Bootstrap 5
- AI功能：OpenAI API（需要自行配置API密钥）

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 初始化数据库

```bash
python init_db.py
```

### 3. 运行应用

```bash
python app.py
```

应用将在 http://127.0.0.1:5000/ 上运行。

## 默认账户

初始化数据库后，系统会创建两个默认账户：

- 管理员账户：
  - 用户名：admin
  - 密码：admin123

- 测试账户：
  - 用户名：test
  - 密码：test123

## 项目结构

```
刷题网站系统/
├── app.py                 # 主应用文件
├── init_db.py             # 数据库初始化脚本
├── requirements.txt       # 依赖包列表
├── static/                # 静态文件
│   ├── css/               # CSS样式
│   │   └── style.css      # 自定义样式
│   └── js/                # JavaScript文件
│       └── main.js        # 自定义脚本
├── templates/             # HTML模板
│   ├── base.html          # 基础模板
│   ├── home.html          # 首页
│   ├── login.html         # 登录页
│   ├── register.html      # 注册页
│   ├── problems.html      # 题库页
│   ├── problem_detail.html # 题目详情页
│   └── search_results.html # 搜索结果页
└── README.md              # 项目说明
```

## AI功能配置

要使用AI功能，需要在环境变量中设置OpenAI API密钥：

```bash
# Windows
set OPENAI_API_KEY=your_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_api_key_here
```

或者在代码中直接设置（不推荐，仅用于开发测试）。

## 未来计划

- 添加用户个人中心
- 实现题目提交和评测功能
- 添加讨论区功能
- 完善AI推荐系统
- 添加学习路径和进度跟踪
