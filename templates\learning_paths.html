{% extends "base.html" %}

{% block title %}学习路径 - 智能刷题平台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-3"><i class="fas fa-road me-2 text-primary"></i>学习路径</h1>
            <p class="lead">选择适合您的学习路径，系统地提升编程技能</p>
        </div>
    </div>
    
    {% if current_user.is_authenticated and enrolled_paths %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-bookmark me-2"></i>我的学习路径</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for path in enrolled_paths %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-header bg-primary bg-opacity-10">
                                    <h5 class="card-title mb-0">{{ path.title }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <span class="badge {% if path.difficulty == 'beginner' %}bg-success{% elif path.difficulty == 'intermediate' %}bg-warning{% else %}bg-danger{% endif %} me-2">
                                            {% if path.difficulty == 'beginner' %}
                                            初级
                                            {% elif path.difficulty == 'intermediate' %}
                                            中级
                                            {% else %}
                                            高级
                                            {% endif %}
                                        </span>
                                        <span class="badge bg-secondary">{{ path.category }}</span>
                                    </div>
                                    <p class="card-text">{{ path.description|truncate(100) }}</p>
                                    
                                    {% set enrollment = current_user.enrollments|selectattr('learning_path_id', 'equalto', path.id)|first %}
                                    {% if enrollment %}
                                    <div class="progress mb-3" style="height: 10px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ (enrollment.current_step / path.steps|length * 100)|round }}%;" 
                                             aria-valuenow="{{ (enrollment.current_step / path.steps|length * 100)|round }}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100"></div>
                                    </div>
                                    <div class="text-muted small mb-3">
                                        进度: {{ enrollment.current_step }}/{{ path.steps|length }} 步骤
                                    </div>
                                    {% endif %}
                                    
                                    <a href="{{ url_for('learning_path_detail', path_id=path.id) }}" class="btn btn-primary">
                                        {% if enrollment and enrollment.completed %}
                                        <i class="fas fa-check-circle me-1"></i>已完成
                                        {% else %}
                                        <i class="fas fa-play me-1"></i>继续学习
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">所有学习路径</h2>
            <div class="btn-group mb-4">
                <button type="button" class="btn btn-outline-primary active" data-filter="all">全部</button>
                <button type="button" class="btn btn-outline-success" data-filter="beginner">初级</button>
                <button type="button" class="btn btn-outline-warning" data-filter="intermediate">中级</button>
                <button type="button" class="btn btn-outline-danger" data-filter="advanced">高级</button>
            </div>
        </div>
    </div>
    
    <div class="row">
        {% if paths %}
        {% for path in paths %}
        <div class="col-md-6 col-lg-4 mb-4 path-card" data-difficulty="{{ path.difficulty }}">
            <div class="card h-100 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{{ path.title }}</h5>
                    <span class="badge {% if path.difficulty == 'beginner' %}bg-success{% elif path.difficulty == 'intermediate' %}bg-warning{% else %}bg-danger{% endif %}">
                        {% if path.difficulty == 'beginner' %}
                        初级
                        {% elif path.difficulty == 'intermediate' %}
                        中级
                        {% else %}
                        高级
                        {% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge bg-secondary">{{ path.category }}</span>
                        <span class="badge bg-info">{{ path.steps|length }} 个步骤</span>
                    </div>
                    <p class="card-text">{{ path.description|truncate(150) }}</p>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('learning_path_detail', path_id=path.id) }}" class="btn btn-primary w-100">
                        <i class="fas fa-info-circle me-1"></i>查看详情
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                暂无学习路径，请稍后再来查看
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 学习路径筛选功能
        const filterButtons = document.querySelectorAll('[data-filter]');
        const pathCards = document.querySelectorAll('.path-card');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 更新按钮状态
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                
                // 筛选卡片
                pathCards.forEach(card => {
                    if (filter === 'all' || card.getAttribute('data-difficulty') === filter) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
{% endblock %}
