{% extends "base.html" %}

{% block title %}搜索结果 - 刷题网站{% endblock %}

{% block content %}
<h1 class="mb-4">搜索结果：{{ query }}</h1>

<div class="card">
    <div class="card-body">
        {% if problems %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>标题</th>
                        <th>分类</th>
                        <th>难度</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for problem in problems %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            {% if problem.id %}
                                <a href="{{ url_for('problem_detail', problem_id=problem.id) }}">{{ problem.title }}</a>
                            {% else %}
                                {{ problem.title }}
                            {% endif %}
                        </td>
                        <td>{{ problem.category }}</td>
                        <td>
                            {% if problem.difficulty == '简单' %}
                            <span class="badge bg-success">简单</span>
                            {% elif problem.difficulty == '中等' %}
                            <span class="badge bg-warning">中等</span>
                            {% else %}
                            <span class="badge bg-danger">困难</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if problem.in_local %}
                                <span class="text-success">已在题库</span>
                            {% else %}
                                <form action="{{ url_for('add_problem') }}" method="post" style="display:inline;">
                                    <input type="hidden" name="title" value="{{ problem.title }}">
                                    <input type="hidden" name="description" value="{{ problem.description }}">
                                    <input type="hidden" name="difficulty" value="{{ problem.difficulty }}">
                                    <input type="hidden" name="category" value="{{ problem.category }}">
                                    <button type="submit" class="btn btn-sm btn-primary">添加到题库</button>
                                </form>
                            {% endif %}
                            <button type="button" class="btn btn-sm btn-outline-secondary ms-2 generate-answer-btn" data-title="{{ problem.title }}" data-description="{{ problem.description }}" data-difficulty="{{ problem.difficulty }}" data-category="{{ problem.category }}">生成答案</button>
                        </td>
                    </tr>
                    <tr class="answer-row" style="display:none;">
                        <td colspan="5"><div class="answer-content"></div></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            没有找到符合条件的题目
        </div>
        {% endif %}
    </div>
</div>

<div class="mt-4">
    <a href="{{ url_for('problems') }}" class="btn btn-outline-primary">返回题库</a>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.generate-answer-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var tr = btn.closest('tr');
            var answerRow = tr.nextElementSibling;
            var answerContent = answerRow.querySelector('.answer-content');
            answerContent.innerHTML = '<span class="text-muted">AI正在生成答案，请稍候...</span>';
            answerRow.style.display = '';
            fetch("{{ url_for('generate_answer') }}", {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: new URLSearchParams({
                    title: btn.dataset.title,
                    description: btn.dataset.description,
                    difficulty: btn.dataset.difficulty,
                    category: btn.dataset.category
                })
            })
            .then(response => response.json())
            .then(data => {
                answerContent.innerHTML = data.answer;
            })
            .catch(() => {
                answerContent.innerHTML = '<span class="text-danger">生成答案失败，请重试。</span>';
            });
        });
    });
});
</script>
{% endblock %}
