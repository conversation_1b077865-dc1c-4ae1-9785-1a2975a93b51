<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智能刷题平台{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-code me-2"></i>智能刷题平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('problems') }}">
                            <i class="fas fa-list-alt me-1"></i>题库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('learning_paths') }}">
                            <i class="fas fa-road me-1"></i>学习路径
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>
                            <span>{{ current_user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('profile') }}">
                                    <i class="fas fa-user me-2"></i>个人中心
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('profile', filter='solved') }}">
                                    <i class="fas fa-check-circle me-2"></i>已解决题目
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('profile', filter='attempted') }}">
                                    <i class="fas fa-spinner me-2"></i>尝试中题目
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('register') }}">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elif category == 'danger' %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% else %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="bg-dark text-white text-center text-lg-start mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-3 mb-lg-0">
                    <h5 class="mb-3"><i class="fas fa-code me-2"></i>智能刷题平台</h5>
                    <p class="small">使用AI技术，为您提供个性化学习体验，让编程学习更高效、更有趣</p>
                </div>
                <div class="col-lg-4 mb-3 mb-lg-0">
                    <h5 class="mb-3">快速链接</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('home') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>首页</a></li>
                        <li><a href="{{ url_for('problems') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>题库</a></li>
                        <li><a href="{{ url_for('learning_paths') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>学习路径</a></li>
                        <li><a href="{{ url_for('login') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>登录</a></li>
                        <li><a href="{{ url_for('register') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>注册</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5 class="mb-3">联系我们</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i>+86 123 4567 8901</li>
                        <li><i class="fas fa-map-marker-alt me-2"></i>北京市海淀区</li>
                    </ul>
                </div>
            </div>
            <hr class="my-3">
            <div class="text-center">
                <p class="small mb-0">© 2023 智能刷题平台 - 使用Flask和Python构建 | <i class="fas fa-robot me-1"></i>AI驱动</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
    {% block scripts %}{% endblock %}
</body>
</html>
