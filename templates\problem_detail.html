{% extends "base.html" %}

{% block title %}{{ problem.title }} - 刷题网站{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3>{{ problem.title }}</h3>
                <span class="badge {% if problem.difficulty == '简单' %}bg-success{% elif problem.difficulty == '中等' %}bg-warning{% else %}bg-danger{% endif %}">
                    {{ problem.difficulty }}
                </span>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <strong>分类：</strong> {{ problem.category }}
                    </div>
                    {% if current_user.is_authenticated %}
                    <div class="problem-status">
                        {% if user_record %}
                            {% if user_record.status == 'solved' %}
                            <span class="badge bg-success me-2"><i class="fas fa-check me-1"></i>已解决</span>
                            {% elif user_record.status == 'attempted' %}
                            <span class="badge bg-warning me-2"><i class="fas fa-spinner me-1"></i>尝试中</span>
                            {% elif user_record.status == 'bookmarked' %}
                            <span class="badge bg-info me-2"><i class="fas fa-bookmark me-1"></i>已收藏</span>
                            {% endif %}
                        {% endif %}
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-flag me-1"></i>更新状态
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <form action="{{ url_for('update_problem_status', problem_id=problem.id, status='solved') }}" method="post">
                                        <button type="submit" class="dropdown-item {% if user_record and user_record.status == 'solved' %}active{% endif %}">
                                            <i class="fas fa-check me-2 text-success"></i>标记为已解决
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form action="{{ url_for('update_problem_status', problem_id=problem.id, status='attempted') }}" method="post">
                                        <button type="submit" class="dropdown-item {% if user_record and user_record.status == 'attempted' %}active{% endif %}">
                                            <i class="fas fa-spinner me-2 text-warning"></i>标记为尝试中
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form action="{{ url_for('update_problem_status', problem_id=problem.id, status='bookmarked') }}" method="post">
                                        <button type="submit" class="dropdown-item {% if user_record and user_record.status == 'bookmarked' %}active{% endif %}">
                                            <i class="fas fa-bookmark me-2 text-info"></i>收藏此题
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="problem-description">
                    {{ problem.description | safe }}
                </div>
            </div>
        </div>

        {% if current_user.is_authenticated %}
        <div class="card mb-4">
            <div class="card-header">
                <h4>提交答案</h4>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="solution" class="form-label">你的解答</label>
                        <textarea class="form-control" id="solution" rows="10"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="language" class="form-label">编程语言</label>
                        <select class="form-select" id="language">
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="cpp">C++</option>
                            <option value="javascript">JavaScript</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">提交</button>
                </form>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            请<a href="{{ url_for('login') }}">登录</a>后提交答案
        </div>
        {% endif %}
    </div>

    <div class="col-md-4">
        {% if current_user.is_authenticated %}
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-lightbulb me-2 text-primary"></i>知识点</h4>
                {% if problem.knowledge_points %}
                <div class="d-flex align-items-center gap-2">
                  <form action="{{ url_for('generate_knowledge', problem_id=problem.id) }}" method="post" onsubmit="return disableBtn(this, 'regen-btn-{{ problem.id }}');">
                      <button id="regen-btn-{{ problem.id }}" type="submit" class="btn btn-outline-primary btn-sm">
                          <i class="fas fa-sync-alt me-1"></i>重新生成
                      </button>
                  </form>
                  <form action="{{ url_for('delete_knowledge', problem_id=problem.id) }}" method="post" onsubmit="return confirm('确定要删除该题目的知识点吗？');">
                      <button type="submit" class="btn btn-outline-danger btn-sm">
                          <i class="fas fa-trash-alt me-1"></i>删除知识点
                      </button>
                  </form>
                </div>
                {% endif %}
            </div>
            <div class="card-body">
                {% if problem.knowledge_points %}
                <div class="knowledge-points p-3 bg-light rounded border card-paged-content" id="knowledge-content">
                    {{ problem.knowledge_points | safe }}
                </div>
                <div class="paged-controls" id="knowledge-paged-controls" style="display:none;">
                    <button class="btn btn-outline-primary btn-sm" id="knowledge-prev">上一页</button>
                    <span id="knowledge-page-info"></span>
                    <button class="btn btn-outline-primary btn-sm" id="knowledge-next">下一页</button>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-brain fa-3x text-muted mb-3"></i>
                    <p>暂无知识点信息</p>
                    <form action="{{ url_for('generate_knowledge', problem_id=problem.id) }}" method="post" onsubmit="return disableBtn(this, 'gen-btn-{{ problem.id }}');">
                        <button id="gen-btn-{{ problem.id }}" type="submit" class="btn btn-primary">
                            <i class="fas fa-magic me-1"></i>使用AI生成知识点
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-video me-2 text-primary"></i>视频解析</h4>
                {% if problem.video_links %}
                <form action="{{ url_for('search_videos', problem_id=problem.id) }}" method="post">
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-sync-alt me-1"></i>重新搜索
                    </button>
                </form>
                {% endif %}
            </div>
            <div class="card-body">
                {% if problem.video_links %}
                <div class="video-links card-paged-content" id="video-content">
                    {{ problem.video_links | safe }}
                </div>
                <div class="paged-controls" id="video-paged-controls" style="display:none;">
                    <button class="btn btn-outline-primary btn-sm" id="video-prev">上一页</button>
                    <span id="video-page-info"></span>
                    <button class="btn btn-outline-primary btn-sm" id="video-next">下一页</button>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-film fa-3x text-muted mb-3"></i>
                    <p>暂无视频解析</p>
                    <form action="{{ url_for('search_videos', problem_id=problem.id) }}" method="post">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>使用AI搜索视频解析
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>
        <!-- 标准答案区块移动到视频卡片下方 -->
        <div class="card mb-4">
            <div class="card-header">
                <h4><i class="fas fa-file-alt me-2 text-primary"></i>标准答案</h4>
            </div>
            <div class="card-body">
                <button id="generate-answer-btn" class="btn btn-outline-secondary w-100 mb-3">生成标准答案</button>
                <div id="answer-content" class="card-paged-content" style="display:none;"></div>
                <div class="paged-controls" id="answer-paged-controls" style="display:none;">
                    <button class="btn btn-outline-primary btn-sm" id="answer-prev">上一页</button>
                    <span id="answer-page-info"></span>
                    <button class="btn btn-outline-primary btn-sm" id="answer-next">下一页</button>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card mb-4">
            <div class="card-header">
                <h4>AI辅助功能</h4>
            </div>
            <div class="card-body">
                <p>登录后可以使用AI生成知识点和搜索视频解析功能</p>
                <a href="{{ url_for('login') }}" class="btn btn-primary">登录</a>
            </div>
        </div>
        {% endif %}

        <div class="card">
            <div class="card-header">
                <h4>相关题目</h4>
            </div>
            <div class="card-body">
                <p>暂无相关题目推荐</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function disableBtn(form, btnId) {
    var btn = document.getElementById(btnId);
    if (btn) {
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>生成中...';
    }
    return true;
}
</script>
{% endblock %}

{% block extra_js %}
{{ super() }}
<!-- 引入marked.js用于前端markdown渲染 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
// 通用分页函数
function paginateContent(content, linesPerPage) {
    const lines = content.split('\n');
    const pages = [];
    for (let i = 0; i < lines.length; i += linesPerPage) {
        pages.push(lines.slice(i, i + linesPerPage).join('\n'));
    }
    return pages;
}
// 答案区块分页
let answerPages = [], answerCurrentPage = 0;
function showAnswerPage(page) {
    if (answerPages.length === 0) return;
    answerCurrentPage = page;
    document.getElementById('answer-content').innerHTML = marked.parse(answerPages[page]);
    document.getElementById('answer-page-info').innerText = (page + 1) + '/' + answerPages.length;
    document.getElementById('answer-prev').disabled = page === 0;
    document.getElementById('answer-next').disabled = page === answerPages.length - 1;
}
document.getElementById('generate-answer-btn').addEventListener('click', function() {
    var btn = this;
    var answerDiv = document.getElementById('answer-content');
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>AI正在生成答案...';
    answerDiv.style.display = '';
    answerDiv.innerHTML = '<span class="text-muted">AI正在生成答案，请稍候...</span>';
    fetch("{{ url_for('generate_answer') }}", {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            title: {{ problem.title|tojson }},
            description: {{ problem.description|tojson }},
            difficulty: {{ problem.difficulty|tojson }},
            category: {{ problem.category|tojson }}
        })
    })
    .then(response => response.json())
    .then(data => {
        answerPages = paginateContent(data.answer, 12);
        document.getElementById('answer-paged-controls').style.display = answerPages.length > 1 ? '' : 'none';
        showAnswerPage(0);
        btn.disabled = false;
        btn.innerHTML = '重新生成标准答案';
    })
    .catch(() => {
        answerDiv.innerHTML = '<span class="text-danger">生成答案失败，请重试。</span>';
        btn.disabled = false;
        btn.innerHTML = '生成标准答案';
    });
});
document.getElementById('answer-prev').addEventListener('click', function() {
    if (answerCurrentPage > 0) showAnswerPage(answerCurrentPage - 1);
});
document.getElementById('answer-next').addEventListener('click', function() {
    if (answerCurrentPage < answerPages.length - 1) showAnswerPage(answerCurrentPage + 1);
});
// 知识点区块分页
(function() {
    var knowledgeDiv = document.getElementById('knowledge-content');
    if (!knowledgeDiv) return;
    var raw = knowledgeDiv.innerText || knowledgeDiv.textContent;
    var pages = paginateContent(raw, 12);
    if (pages.length > 1) {
        document.getElementById('knowledge-paged-controls').style.display = '';
        let current = 0;
        function show(page) {
            current = page;
            knowledgeDiv.innerHTML = marked.parse(pages[page]);
            document.getElementById('knowledge-page-info').innerText = (page + 1) + '/' + pages.length;
            document.getElementById('knowledge-prev').disabled = page === 0;
            document.getElementById('knowledge-next').disabled = page === pages.length - 1;
        }
        document.getElementById('knowledge-prev').onclick = function() { if (current > 0) show(current - 1); };
        document.getElementById('knowledge-next').onclick = function() { if (current < pages.length - 1) show(current + 1); };
        show(0);
    }
})();
// 视频区块分页
(function() {
    var videoDiv = document.getElementById('video-content');
    if (!videoDiv) return;
    var raw = videoDiv.innerText || videoDiv.textContent;
    var pages = paginateContent(raw, 12);
    if (pages.length > 1) {
        document.getElementById('video-paged-controls').style.display = '';
        let current = 0;
        function show(page) {
            current = page;
            videoDiv.innerHTML = marked.parse(pages[page]);
            document.getElementById('video-page-info').innerText = (page + 1) + '/' + pages.length;
            document.getElementById('video-prev').disabled = page === 0;
            document.getElementById('video-next').disabled = page === pages.length - 1;
        }
        document.getElementById('video-prev').onclick = function() { if (current > 0) show(current - 1); };
        document.getElementById('video-next').onclick = function() { if (current < pages.length - 1) show(current + 1); };
        show(0);
    }
})();
// 视频解析生成按钮 loading 效果
(function() {
    var videoForm = document.querySelector('form[action$="search_videos"]');
    if (videoForm) {
        var videoBtn = videoForm.querySelector('button[type="submit"]');
        videoForm.addEventListener('submit', function() {
            if (videoBtn) {
                videoBtn.disabled = true;
                videoBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>搜索中...';
            }
        });
    }
})();
// 标准答案生成按钮 loading 效果
(function() {
    var btn = document.getElementById('generate-answer-btn');
    if (!btn) return;
    var answerDiv = document.getElementById('answer-content');
    btn.addEventListener('click', function() {
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>AI正在生成答案...';
        answerDiv.style.display = '';
        answerDiv.innerHTML = '<span class="text-muted">AI正在生成答案，请稍候...</span>';
    });
})();
</script>
{% endblock %}
