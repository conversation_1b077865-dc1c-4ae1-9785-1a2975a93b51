from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, session
from markupsafe import Markup
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import os
import pymysql
from datetime import datetime, timedelta
import requests
import json
import markdown
from config import config
from ai_service import AIService
from session_config import init_session

# 初始化数据库
db = SQLAlchemy()

# 初始化登录管理器
login_manager = LoginManager()
login_manager.login_view = 'login'
login_manager.login_message = '请先登录才能访问此页面'

def create_app(config_name='mysql'):
    # 初始化Flask应用
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)

    # 初始化会话
    init_session(app)

    app.session_cookie_name = app.config.get('SESSION_COOKIE_NAME', 'session')

    return app

# 创建应用实例
app = create_app('mysql')

# 用户模型
class User(db.Model, UserMixin):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)  # 增加长度以适应哈希密码
    date_registered = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime, nullable=True)  # 记录最后登录时间
    login_count = db.Column(db.Integer, default=0)  # 记录登录次数
    is_active = db.Column(db.Boolean, default=True)  # 账号是否激活
    records = db.relationship('UserRecord', backref='user', lazy=True)
    registration_info = db.relationship('UserRegistrationInfo', backref='user', uselist=False)

    def __repr__(self):
        return f"User('{self.username}', '{self.email}')"

# 用户注册信息表
class UserRegistrationInfo(db.Model):
    __tablename__ = 'user_registration_info'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    ip_address = db.Column(db.String(45), nullable=True)  # 支持IPv6
    user_agent = db.Column(db.String(255), nullable=True)  # 浏览器信息
    registration_date = db.Column(db.DateTime, default=datetime.now)
    referrer = db.Column(db.String(255), nullable=True)  # 来源网站

    def __repr__(self):
        return f"UserRegistrationInfo(user_id: {self.user_id}, date: {self.registration_date})"

# 题目模型
class Problem(db.Model):
    __tablename__ = 'problems'
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    difficulty = db.Column(db.String(20), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    knowledge_points = db.Column(db.Text, nullable=True)
    video_links = db.Column(db.Text, nullable=True)
    date_added = db.Column(db.DateTime, default=datetime.now)
    records = db.relationship('UserRecord', backref='problem', lazy=True)

    def __repr__(self):
        return f"Problem('{self.title}', '{self.difficulty}')"

# 用户做题记录模型
class UserRecord(db.Model):
    __tablename__ = 'user_records'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    problem_id = db.Column(db.Integer, db.ForeignKey('problems.id'), nullable=False)
    status = db.Column(db.String(20), nullable=False)  # 'solved', 'attempted', 'bookmarked'
    date_attempted = db.Column(db.DateTime, default=datetime.now)
    score = db.Column(db.Integer, nullable=True)  # 可选的得分字段
    time_spent = db.Column(db.Integer, nullable=True)  # 可选的耗时字段（秒）

    def __repr__(self):
        return f"UserRecord(User: {self.user_id}, Problem: {self.problem_id}, Status: {self.status})"

# 学习路径模型
class LearningPath(db.Model):
    __tablename__ = 'learning_paths'
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    difficulty = db.Column(db.String(20), nullable=False)  # 'beginner', 'intermediate', 'advanced'
    category = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    steps = db.relationship('LearningPathStep', backref='learning_path', lazy=True, order_by='LearningPathStep.order')
    enrollments = db.relationship('UserPathEnrollment', backref='learning_path', lazy=True)

    def __repr__(self):
        return f"LearningPath('{self.title}', '{self.difficulty}')"

# 学习路径步骤模型
class LearningPathStep(db.Model):
    __tablename__ = 'learning_path_steps'
    id = db.Column(db.Integer, primary_key=True)
    learning_path_id = db.Column(db.Integer, db.ForeignKey('learning_paths.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    order = db.Column(db.Integer, nullable=False)  # 步骤顺序
    problem_id = db.Column(db.Integer, db.ForeignKey('problems.id'), nullable=True)  # 关联的题目，可选
    content = db.Column(db.Text, nullable=True)  # 学习内容，可选

    def __repr__(self):
        return f"LearningPathStep('{self.title}', order: {self.order})"

# 用户学习路径注册模型
class UserPathEnrollment(db.Model):
    __tablename__ = 'user_path_enrollments'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    learning_path_id = db.Column(db.Integer, db.ForeignKey('learning_paths.id'), nullable=False)
    enrolled_at = db.Column(db.DateTime, default=datetime.now)
    current_step = db.Column(db.Integer, default=1)  # 当前进行到的步骤
    completed = db.Column(db.Boolean, default=False)  # 是否完成
    completed_at = db.Column(db.DateTime, nullable=True)  # 完成时间

    def __repr__(self):
        return f"UserPathEnrollment(User: {self.user_id}, Path: {self.learning_path_id}, Step: {self.current_step})"

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 路由：首页
@app.route('/')
def home():
    problems = Problem.query.order_by(Problem.date_added.desc()).limit(5).all()
    return render_template('home.html', problems=problems)

# 路由：注册
@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        user_by_username = User.query.filter_by(username=username).first()
        user_by_email = User.query.filter_by(email=email).first()

        if user_by_username:
            flash('该用户名已被注册，请选择其他用户名', 'danger')
        elif user_by_email:
            flash('该邮箱已被注册，请使用其他邮箱', 'danger')
        elif password != confirm_password:
            flash('两次输入的密码不匹配', 'danger')
        else:
            # 创建新用户
            hashed_password = generate_password_hash(password)
            new_user = User(
                username=username,
                email=email,
                password=hashed_password,
                date_registered=datetime.now(),
                is_active=True
            )
            db.session.add(new_user)
            db.session.flush()  # 获取新用户ID

            # 记录注册信息
            registration_info = UserRegistrationInfo(
                user_id=new_user.id,
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string if request.user_agent else None,
                referrer=request.referrer
            )
            db.session.add(registration_info)

            db.session.commit()
            flash('注册成功！现在您可以登录了', 'success')
            return redirect(url_for('login'))

    return render_template('register.html')

# 路由：登录
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            # 更新用户登录信息
            user.last_login = datetime.now()
            user.login_count += 1
            db.session.commit()

            # 设置会话为永久会话
            session.permanent = True

            # 登录用户
            login_user(user, remember=remember)

            next_page = request.args.get('next')
            flash(f'欢迎回来，{user.username}！', 'success')
            return redirect(next_page) if next_page else redirect(url_for('home'))
        else:
            flash('登录失败，请检查用户名和密码', 'danger')

    return render_template('login.html')

# 路由：登出
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('您已成功登出', 'success')
    return redirect(url_for('home'))

# 路由：题库
@app.route('/problems')
def problems():
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', None)
    difficulty = request.args.get('difficulty', None)
    search = request.args.get('search', None)

    query = Problem.query

    if category:
        query = query.filter_by(category=category)
    if difficulty:
        query = query.filter_by(difficulty=difficulty)
    if search:
        query = query.filter(Problem.title.contains(search) | Problem.description.contains(search))

    problems = query.order_by(Problem.id).paginate(page=page, per_page=10)

    return render_template('problems.html', problems=problems)

# 路由：题目详情
@app.route('/problem/<int:problem_id>')
def problem_detail(problem_id):
    problem = Problem.query.get_or_404(problem_id)

    # 如果用户已登录，检查是否有做题记录
    user_record = None
    if current_user.is_authenticated:
        user_record = UserRecord.query.filter_by(
            user_id=current_user.id,
            problem_id=problem.id
        ).first()

    return render_template('problem_detail.html', problem=problem, user_record=user_record)

# 路由：更新做题状态
@app.route('/update_problem_status/<int:problem_id>/<status>', methods=['POST'])
@login_required
def update_problem_status(problem_id, status):
    if status not in ['solved', 'attempted', 'bookmarked']:
        flash('无效的状态', 'danger')
        return redirect(url_for('problem_detail', problem_id=problem_id))

    # 检查题目是否存在
    problem = Problem.query.get_or_404(problem_id)

    # 检查是否已有记录
    record = UserRecord.query.filter_by(
        user_id=current_user.id,
        problem_id=problem_id
    ).first()

    if record:
        # 更新现有记录
        record.status = status
        record.date_attempted = datetime.now()
    else:
        # 创建新记录
        record = UserRecord(
            user_id=current_user.id,
            problem_id=problem_id,
            status=status,
            date_attempted=datetime.now()
        )
        db.session.add(record)

    db.session.commit()

    status_text = {
        'solved': '已解决',
        'attempted': '尝试中',
        'bookmarked': '已收藏'
    }

    flash(f'题目状态已更新为"{status_text[status]}"', 'success')
    return redirect(url_for('problem_detail', problem_id=problem_id))

# 路由：用户个人中心
@app.route('/profile')
@app.route('/profile/<filter>')
@login_required
def profile(filter='all'):
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 根据筛选条件获取记录
    query = UserRecord.query.filter_by(user_id=current_user.id)

    if filter == 'solved':
        query = query.filter_by(status='solved')
    elif filter == 'attempted':
        query = query.filter_by(status='attempted')

    # 获取分页记录
    pagination = query.order_by(UserRecord.date_attempted.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    records = pagination.items

    # 计算统计信息
    total_problems = Problem.query.count()
    solved_count = UserRecord.query.filter_by(user_id=current_user.id, status='solved').count()
    attempted_count = UserRecord.query.filter_by(user_id=current_user.id, status='attempted').count()

    # 计算进度
    progress = round((solved_count / total_problems) * 100) if total_problems > 0 else 0

    # 按难度统计
    easy_total = Problem.query.filter_by(difficulty='简单').count()
    medium_total = Problem.query.filter_by(difficulty='中等').count()
    hard_total = Problem.query.filter_by(difficulty='困难').count()

    # 按难度统计已解决的题目
    easy_solved = UserRecord.query.join(Problem).filter(
        UserRecord.user_id == current_user.id,
        UserRecord.status == 'solved',
        Problem.difficulty == '简单'
    ).count()

    medium_solved = UserRecord.query.join(Problem).filter(
        UserRecord.user_id == current_user.id,
        UserRecord.status == 'solved',
        Problem.difficulty == '中等'
    ).count()

    hard_solved = UserRecord.query.join(Problem).filter(
        UserRecord.user_id == current_user.id,
        UserRecord.status == 'solved',
        Problem.difficulty == '困难'
    ).count()

    # 计算各难度的进度
    easy_progress = round((easy_solved / easy_total) * 100) if easy_total > 0 else 0
    medium_progress = round((medium_solved / medium_total) * 100) if medium_total > 0 else 0
    hard_progress = round((hard_solved / hard_total) * 100) if hard_total > 0 else 0

    # 统计信息
    stats = {
        'total_problems': total_problems,
        'solved_count': solved_count,
        'attempted_count': attempted_count,
        'progress': progress,
        'easy_total': easy_total,
        'medium_total': medium_total,
        'hard_total': hard_total,
        'easy_solved': easy_solved,
        'medium_solved': medium_solved,
        'hard_solved': hard_solved,
        'easy_progress': easy_progress,
        'medium_progress': medium_progress,
        'hard_progress': hard_progress
    }

    return render_template('profile.html',
                          records=records,
                          pagination=pagination,
                          filter=filter,
                          stats=stats)

# 路由：AI搜索题目
@app.route('/ai_search', methods=['POST'])
def ai_search():
    query = request.form.get('query')
    if not query:
        flash('请输入搜索内容', 'warning')
        return redirect(url_for('problems'))

    try:
        # 获取所有题目
        all_problems = Problem.query.all()
        # 使用AI服务搜索题目，返回题目完整信息（dict）
        ai_results = AIService.search_problems(query, all_problems)

        # 获取本地题目标题集合（用于去重）
        local_titles = set([p.title for p in all_problems])
        # 标记哪些题目已在本地
        for item in ai_results:
            item['in_local'] = item['title'] in local_titles

        return render_template('search_results.html', problems=ai_results, query=query)
    except Exception as e:
        flash(f'搜索过程中出现错误: {str(e)}', 'danger')
        return redirect(url_for('problems'))

# 路由：添加AI题目到题库
@app.route('/add_problem', methods=['POST'])
@login_required
def add_problem():
    title = request.form.get('title')
    description = request.form.get('description')
    difficulty = request.form.get('difficulty')
    category = request.form.get('category')
    # 检查是否已存在
    if Problem.query.filter_by(title=title).first():
        flash('题目已存在于题库', 'info')
        return redirect(url_for('problems'))
    # 添加到数据库
    new_problem = Problem(
        title=title,
        description=description,
        difficulty=difficulty,
        category=category
    )
    db.session.add(new_problem)
    db.session.commit()
    flash('题目已成功添加到题库', 'success')
    return redirect(url_for('problems'))

# 路由：生成知识点
@app.route('/generate_knowledge/<int:problem_id>', methods=['POST'])
@login_required
def generate_knowledge(problem_id):
    problem = Problem.query.get_or_404(problem_id)

    try:
        # 只在知识点为空时才生成
        if not problem.knowledge_points:
            knowledge_md = AIService.generate_knowledge_points(problem)
            try:
                knowledge_html = markdown.markdown(knowledge_md, extensions=['extra'])
            except:
                knowledge_html = f"<pre>{knowledge_md}</pre>"
            problem.knowledge_points = knowledge_html
            db.session.commit()
            flash('知识点生成成功！', 'success')
        else:
            flash('知识点已存在，无需重复生成。', 'info')
    except Exception as e:
        flash(f'生成知识点时出现错误: {str(e)}', 'danger')

    return redirect(url_for('problem_detail', problem_id=problem.id))

# 路由：搜索视频解析
@app.route('/search_videos/<int:problem_id>', methods=['POST'])
@login_required
def search_videos(problem_id):
    problem = Problem.query.get_or_404(problem_id)

    try:
        # 使用AI服务搜索视频
        video_info = AIService.search_videos(problem)

        # 将Markdown转换为HTML
        try:
            video_html = markdown.markdown(video_info, extensions=['extra'])
        except:
            # 如果markdown转换失败，直接使用原始文本
            video_html = f"<pre>{video_info}</pre>"

        # 保存视频信息
        problem.video_links = video_html
        db.session.commit()

        flash('视频解析搜索成功！', 'success')
    except Exception as e:
        flash(f'搜索视频时出现错误: {str(e)}', 'danger')

    return redirect(url_for('problem_detail', problem_id=problem.id))

# 路由：设置API密钥
@app.route('/set_api_key', methods=['POST'])
def set_api_key():
    api_key = request.form.get('api_key')
    ai_type = request.form.get('ai_type')
    session['api_key'] = api_key
    session['ai_type'] = ai_type
    flash('API 密钥和类型已设置，仅保存在本地会话。', 'success')
    return redirect(url_for('home'))

# 路由：学习路径列表
@app.route('/learning_paths')
def learning_paths():
    paths = LearningPath.query.all()

    # 如果用户已登录，获取已注册的路径
    enrolled_paths = []
    if current_user.is_authenticated:
        enrollments = UserPathEnrollment.query.filter_by(user_id=current_user.id).all()
        enrolled_path_ids = [e.learning_path_id for e in enrollments]
        enrolled_paths = LearningPath.query.filter(LearningPath.id.in_(enrolled_path_ids)).all()

    return render_template('learning_paths.html', paths=paths, enrolled_paths=enrolled_paths)

# 路由：学习路径详情
@app.route('/learning_path/<int:path_id>')
def learning_path_detail(path_id):
    path = LearningPath.query.get_or_404(path_id)

    # 检查用户是否已注册该路径
    enrollment = None
    if current_user.is_authenticated:
        enrollment = UserPathEnrollment.query.filter_by(
            user_id=current_user.id,
            learning_path_id=path_id
        ).first()

    return render_template('learning_path_detail.html', path=path, enrollment=enrollment)

# 路由：注册学习路径
@app.route('/enroll_path/<int:path_id>', methods=['POST'])
@login_required
def enroll_path(path_id):
    # 检查路径是否存在
    path = LearningPath.query.get_or_404(path_id)

    # 检查是否已注册
    existing_enrollment = UserPathEnrollment.query.filter_by(
        user_id=current_user.id,
        learning_path_id=path_id
    ).first()

    if existing_enrollment:
        flash('您已经注册了这个学习路径', 'info')
    else:
        # 创建新的注册记录
        enrollment = UserPathEnrollment(
            user_id=current_user.id,
            learning_path_id=path_id,
            current_step=1,
            enrolled_at=datetime.now()
        )
        db.session.add(enrollment)
        db.session.commit()
        flash(f'成功注册学习路径：{path.title}', 'success')

    return redirect(url_for('learning_path_detail', path_id=path_id))

# 路由：更新学习进度
@app.route('/update_path_progress/<int:path_id>/<int:step>', methods=['POST'])
@login_required
def update_path_progress(path_id, step):
    # 检查路径是否存在
    path = LearningPath.query.get_or_404(path_id)

    # 检查步骤是否有效
    if step < 1 or step > len(path.steps):
        flash('无效的步骤', 'danger')
        return redirect(url_for('learning_path_detail', path_id=path_id))

    # 检查是否已注册
    enrollment = UserPathEnrollment.query.filter_by(
        user_id=current_user.id,
        learning_path_id=path_id
    ).first()

    if not enrollment:
        flash('您尚未注册此学习路径', 'warning')
        return redirect(url_for('learning_path_detail', path_id=path_id))

    # 更新进度
    enrollment.current_step = step

    # 如果是最后一步，标记为完成
    if step == len(path.steps):
        enrollment.completed = True
        enrollment.completed_at = datetime.now()
        flash('恭喜您完成了这个学习路径！', 'success')
    else:
        flash('学习进度已更新', 'success')

    db.session.commit()
    return redirect(url_for('learning_path_detail', path_id=path_id))

# 路由：删除知识点
@app.route('/delete_knowledge/<int:problem_id>', methods=['POST'])
@login_required
def delete_knowledge(problem_id):
    problem = Problem.query.get_or_404(problem_id)
    problem.knowledge_points = None
    db.session.commit()
    flash('知识点已删除。', 'success')
    return redirect(url_for('problem_detail', problem_id=problem.id))

# 路由：生成AI标准答案（AJAX）
@app.route('/generate_answer', methods=['POST'])
@login_required
def generate_answer():
    title = request.form.get('title')
    description = request.form.get('description')
    difficulty = request.form.get('difficulty')
    category = request.form.get('category')
    problem = {
        'title': title,
        'description': description,
        'difficulty': difficulty,
        'category': category
    }
    answer_md = AIService.generate_answer(problem)
    # 转为HTML
    try:
        answer_html = Markup(markdown.markdown(answer_md, extensions=['extra']))
    except:
        answer_html = f"<pre>{answer_md}</pre>"
    return jsonify({'answer': str(answer_html)})

# 创建数据库表
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True)
