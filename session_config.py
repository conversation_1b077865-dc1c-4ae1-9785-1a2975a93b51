from flask_session import Session
import os
from datetime import timedelta

def init_session(app):
    """初始化Flask-Session"""
    # 会话配置
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)  # 会话持续7天
    app.config['SESSION_TYPE'] = 'filesystem'  # 使用文件系统存储会话
    app.config['SESSION_PERMANENT'] = True
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_FILE_DIR'] = os.path.join(os.getcwd(), 'flask_session')  # 会话文件存储路径
    
    # 初始化Flask-Session
    Session(app)
    
    return app
