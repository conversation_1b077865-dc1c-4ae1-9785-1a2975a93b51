from app import app, db, User, Problem, UserRecord
from werkzeug.security import generate_password_hash
from datetime import datetime

# 在应用上下文中执行数据库操作
with app.app_context():
    # 重新创建所有表
    db.drop_all()
    db.create_all()
    
    # 创建示例用户
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        password=generate_password_hash('admin123')
    )
    
    test_user = User(
        username='test',
        email='<EMAIL>',
        password=generate_password_hash('test123')
    )
    
    db.session.add(admin_user)
    db.session.add(test_user)
    
    # 创建示例题目
    problems = [
        Problem(
            title='两数之和',
            description='''
            <p>给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值 target 的那两个整数，并返回它们的数组下标。</p>
            <p>你可以假设每种输入只会对应一个答案。但是，数组中同一个元素在答案里不能重复出现。</p>
            <p>你可以按任意顺序返回答案。</p>
            
            <p><strong>示例 1：</strong></p>
            <pre>
            输入：nums = [2,7,11,15], target = 9
            输出：[0,1]
            解释：因为 nums[0] + nums[1] == 9 ，返回 [0, 1] 。
            </pre>
            
            <p><strong>示例 2：</strong></p>
            <pre>
            输入：nums = [3,2,4], target = 6
            输出：[1,2]
            </pre>
            
            <p><strong>示例 3：</strong></p>
            <pre>
            输入：nums = [3,3], target = 6
            输出：[0,1]
            </pre>
            ''',
            difficulty='简单',
            category='算法',
            date_added=datetime.utcnow()
        ),
        Problem(
            title='合并两个有序链表',
            description='''
            <p>将两个升序链表合并为一个新的 <strong>升序</strong> 链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。</p>
            
            <p><strong>示例 1：</strong></p>
            <pre>
            输入：l1 = [1,2,4], l2 = [1,3,4]
            输出：[1,1,2,3,4,4]
            </pre>
            
            <p><strong>示例 2：</strong></p>
            <pre>
            输入：l1 = [], l2 = []
            输出：[]
            </pre>
            
            <p><strong>示例 3：</strong></p>
            <pre>
            输入：l1 = [], l2 = [0]
            输出：[0]
            </pre>
            ''',
            difficulty='简单',
            category='数据结构',
            date_added=datetime.utcnow()
        ),
        Problem(
            title='最长回文子串',
            description='''
            <p>给你一个字符串 s，找到 s 中最长的回文子串。</p>
            <p>如果字符串的反序与原始字符串相同，则该字符串称为回文字符串。</p>
            
            <p><strong>示例 1：</strong></p>
            <pre>
            输入：s = "babad"
            输出："bab"
            解释："aba" 同样是符合题意的答案。
            </pre>
            
            <p><strong>示例 2：</strong></p>
            <pre>
            输入：s = "cbbd"
            输出："bb"
            </pre>
            ''',
            difficulty='中等',
            category='算法',
            date_added=datetime.utcnow()
        ),
        Problem(
            title='二叉树的层序遍历',
            description='''
            <p>给你二叉树的根节点 root ，返回其节点值的 <strong>层序遍历</strong> 。 （即逐层地，从左到右访问所有节点）。</p>
            
            <p><strong>示例 1：</strong></p>
            <pre>
            输入：root = [3,9,20,null,null,15,7]
            输出：[[3],[9,20],[15,7]]
            </pre>
            
            <p><strong>示例 2：</strong></p>
            <pre>
            输入：root = [1]
            输出：[[1]]
            </pre>
            
            <p><strong>示例 3：</strong></p>
            <pre>
            输入：root = []
            输出：[]
            </pre>
            ''',
            difficulty='中等',
            category='数据结构',
            date_added=datetime.utcnow()
        ),
        Problem(
            title='接雨水',
            description='''
            <p>给定 n 个非负整数表示每个宽度为 1 的柱子的高度图，计算按此排列的柱子，下雨之后能接多少雨水。</p>
            
            <p><strong>示例 1：</strong></p>
            <pre>
            输入：height = [0,1,0,2,1,0,1,3,2,1,2,1]
            输出：6
            解释：上面是由数组 [0,1,0,2,1,0,1,3,2,1,2,1] 表示的高度图，在这种情况下，可以接 6 个单位的雨水（蓝色部分表示雨水）。
            </pre>
            
            <p><strong>示例 2：</strong></p>
            <pre>
            输入：height = [4,2,0,3,2,5]
            输出：9
            </pre>
            ''',
            difficulty='困难',
            category='算法',
            date_added=datetime.utcnow()
        ),
        Problem(
            title='SQL查询优化',
            description='''
            <p>给定以下数据库表结构：</p>
            <pre>
            CREATE TABLE Customers (
                id INT PRIMARY KEY,
                name VARCHAR(100),
                email VARCHAR(100),
                created_at TIMESTAMP
            );
            
            CREATE TABLE Orders (
                id INT PRIMARY KEY,
                customer_id INT,
                amount DECIMAL(10, 2),
                order_date TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES Customers(id)
            );
            </pre>
            
            <p>请编写一个SQL查询，找出下单金额超过1000元的客户的姓名和邮箱，并按照下单金额降序排列。</p>
            ''',
            difficulty='中等',
            category='数据库',
            date_added=datetime.utcnow()
        ),
        Problem(
            title='正则表达式匹配',
            description='''
            <p>给你一个字符串 s 和一个字符规律 p，请你来实现一个支持 '.' 和 '*' 的正则表达式匹配。</p>
            <ul>
                <li>'.' 匹配任意单个字符</li>
                <li>'*' 匹配零个或多个前面的那一个元素</li>
            </ul>
            <p>所谓匹配，是要涵盖 <strong>整个</strong> 字符串 s的，而不是部分字符串。</p>
            
            <p><strong>示例 1：</strong></p>
            <pre>
            输入：s = "aa", p = "a"
            输出：false
            解释："a" 无法匹配 "aa" 整个字符串。
            </pre>
            
            <p><strong>示例 2：</strong></p>
            <pre>
            输入：s = "aa", p = "a*"
            输出：true
            解释：因为 '*' 代表可以匹配零个或多个前面的那一个元素, 在这里前面的元素就是 'a'。因此，字符串 "aa" 可被视为 'a' 重复了一次。
            </pre>
            
            <p><strong>示例 3：</strong></p>
            <pre>
            输入：s = "ab", p = ".*"
            输出：true
            解释：".*" 表示可匹配零个或多个（'*'）任意字符（'.'）。
            </pre>
            ''',
            difficulty='困难',
            category='算法',
            date_added=datetime.utcnow()
        ),
    ]
    
    for problem in problems:
        db.session.add(problem)
    
    # 创建一些用户做题记录
    records = [
        UserRecord(
            user_id=1,
            problem_id=1,
            status='solved'
        ),
        UserRecord(
            user_id=1,
            problem_id=2,
            status='solved'
        ),
        UserRecord(
            user_id=2,
            problem_id=1,
            status='attempted'
        )
    ]
    
    for record in records:
        db.session.add(record)
    
    # 提交所有更改
    db.session.commit()
    
    print("数据库初始化完成！")
