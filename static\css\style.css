/* 全局样式 */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4cc9f0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

.container {
    flex: 1;
    padding-top: 20px;
    padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    transition: var(--transition);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
    transform: translateY(-2px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 20px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 8px 24px rgba(67, 97, 238, 0.12);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 首页样式 */
.jumbotron {
    background: linear-gradient(120deg, var(--primary-color) 60%, var(--accent-color) 100%);
    color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.jumbotron .btn-primary {
    background: #fff;
    color: var(--primary-color);
    border: none;
    font-weight: 700;
}

.jumbotron .btn-primary:hover {
    background: #f8f9fa;
    color: var(--secondary-color);
}

.jumbotron .btn-outline-light {
    border: 2px solid #fff;
    color: #fff;
    font-weight: 700;
}

.jumbotron .btn-outline-light:hover {
    background: #fff;
    color: var(--primary-color);
}

/* 题目详情页样式 */
.problem-description {
    font-size: 1.1rem;
    line-height: 1.8;
    padding: 15px;
    background-color: #fff;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.problem-description pre {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid var(--primary-color);
}

.knowledge-points {
    background-color: #fff;
    padding: 20px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    border-left: 4px solid var(--accent-color);
    margin-bottom: 2rem;
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 0.8em;
    font-weight: 500;
    border-radius: 30px;
}

.badge.bg-success, .badge.bg-warning, .badge.bg-danger {
    font-size: 1em;
    padding: 0.5em 1em;
    border-radius: 20px;
    font-weight: 600;
}

/* 分页样式 */
.pagination {
    margin-top: 20px;
}

.page-link {
    color: var(--primary-color);
    border: none;
    margin: 0 3px;
    border-radius: 5px;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    padding: 0.6rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-left: none;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
    border-color: var(--primary-color);
}

/* 代码编辑器样式 */
#solution {
    font-family: 'Courier New', Courier, monospace;
    background-color: #f8f9fa;
    border-left: 4px solid var(--primary-color);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.8s;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem;
    }

    .card {
        margin-bottom: 15px;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* 首页特色 icon 圆圈 */
.feature-icon {
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.08);
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* AI 搜索输入框美化 */
.input-group-text {
    background: #fff;
    border-right: none;
}

/* 平台特色区块 */
.card .feature-icon.bg-light {
    background: #f5f7fa !important;
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* 固定高度卡片内容区，分页模式 */
.card-paged-content {
    min-height: 180px;
    max-height: 320px;
    overflow: hidden;
    position: relative;
    padding-bottom: 3.5rem; /* 增大底部空间，避免内容和按钮重叠 */
    background: #f8f9fa;
    border-radius: var(--border-radius);
    border: 1px solid #e0e0e0;
    box-sizing: border-box;
}

.paged-controls {
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    padding-bottom: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(to top, #f8f9fa 90%, rgba(248,249,250,0));
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    z-index: 2;
}

.paged-controls .btn {
    margin: 0 0.5rem;
    padding: 0.3rem 1.2rem;
    font-size: 1rem;
}
