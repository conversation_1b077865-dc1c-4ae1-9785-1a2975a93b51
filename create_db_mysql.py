import pymysql

def create_database():
    try:
        conn = pymysql.connect(host='localhost', user='root', password='123456', charset='utf8mb4')
        cursor = conn.cursor()
        cursor.execute("CREATE DATABASE IF NOT EXISTS problem_site CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("数据库创建成功！")
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"创建数据库出错: {str(e)}")

if __name__ == "__main__":
    create_database()