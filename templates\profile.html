{% extends "base.html" %}

{% block title %}个人中心 - {{ current_user.username }} - 智能刷题平台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- 用户信息卡片 -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-user-circle me-2"></i>个人信息</h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle mx-auto mb-3">
                            <span class="avatar-text">{{ current_user.username[0]|upper }}</span>
                        </div>
                        <h4>{{ current_user.username }}</h4>
                        <p class="text-muted">{{ current_user.email }}</p>
                    </div>
                    
                    <div class="user-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-value">{{ stats.solved_count }}</div>
                                <div class="stat-label">已解决</div>
                            </div>
                            <div class="col-4">
                                <div class="stat-value">{{ stats.attempted_count }}</div>
                                <div class="stat-label">尝试中</div>
                            </div>
                            <div class="col-4">
                                <div class="stat-value">{{ stats.total_problems }}</div>
                                <div class="stat-label">总题数</div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="user-info">
                        <div class="info-item d-flex justify-content-between">
                            <span><i class="fas fa-calendar-alt me-2 text-primary"></i>注册日期</span>
                            <span>{{ current_user.date_registered.strftime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between">
                            <span><i class="fas fa-sign-in-alt me-2 text-primary"></i>上次登录</span>
                            <span>{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else '无记录' }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between">
                            <span><i class="fas fa-history me-2 text-primary"></i>登录次数</span>
                            <span>{{ current_user.login_count }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 进度卡片 -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-chart-pie me-2"></i>刷题进度</h4>
                </div>
                <div class="card-body">
                    <div class="progress-stats mb-4">
                        <h5 class="text-center mb-3">总体进度</h5>
                        <div class="progress mb-2" style="height: 20px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ stats.progress }}%;" aria-valuenow="{{ stats.progress }}" aria-valuemin="0" aria-valuemax="100">{{ stats.progress }}%</div>
                        </div>
                        <div class="text-center text-muted small">已完成 {{ stats.solved_count }}/{{ stats.total_problems }} 题</div>
                    </div>
                    
                    <div class="difficulty-stats">
                        <h5 class="text-center mb-3">难度分布</h5>
                        <div class="difficulty-item mb-2">
                            <div class="d-flex justify-content-between mb-1">
                                <span><i class="fas fa-circle text-success me-1"></i>简单</span>
                                <span>{{ stats.easy_solved }}/{{ stats.easy_total }}</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ stats.easy_progress }}%;" aria-valuenow="{{ stats.easy_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="difficulty-item mb-2">
                            <div class="d-flex justify-content-between mb-1">
                                <span><i class="fas fa-circle text-warning me-1"></i>中等</span>
                                <span>{{ stats.medium_solved }}/{{ stats.medium_total }}</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ stats.medium_progress }}%;" aria-valuenow="{{ stats.medium_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="difficulty-item">
                            <div class="d-flex justify-content-between mb-1">
                                <span><i class="fas fa-circle text-danger me-1"></i>困难</span>
                                <span>{{ stats.hard_solved }}/{{ stats.hard_total }}</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: {{ stats.hard_progress }}%;" aria-valuenow="{{ stats.hard_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 做题记录 -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-history me-2"></i>做题记录</h4>
                    <div class="btn-group">
                        <a href="{{ url_for('profile', filter='all') }}" class="btn btn-sm {{ 'btn-light' if filter == 'all' else 'btn-outline-light' }}">全部</a>
                        <a href="{{ url_for('profile', filter='solved') }}" class="btn btn-sm {{ 'btn-light' if filter == 'solved' else 'btn-outline-light' }}">已解决</a>
                        <a href="{{ url_for('profile', filter='attempted') }}" class="btn btn-sm {{ 'btn-light' if filter == 'attempted' else 'btn-outline-light' }}">尝试中</a>
                    </div>
                </div>
                <div class="card-body">
                    {% if records %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>题目</th>
                                    <th>难度</th>
                                    <th>状态</th>
                                    <th>最后尝试时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in records %}
                                <tr>
                                    <td><a href="{{ url_for('problem_detail', problem_id=record.problem.id) }}">{{ record.problem.title }}</a></td>
                                    <td>
                                        <span class="badge {% if record.problem.difficulty == '简单' %}bg-success{% elif record.problem.difficulty == '中等' %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ record.problem.difficulty }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {% if record.status == 'solved' %}bg-success{% elif record.status == 'attempted' %}bg-warning{% else %}bg-secondary{% endif %}">
                                            {% if record.status == 'solved' %}已解决{% elif record.status == 'attempted' %}尝试中{% else %}已收藏{% endif %}
                                        </span>
                                    </td>
                                    <td>{{ record.date_attempted.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('problem_detail', problem_id=record.problem.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-code"></i> 继续
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('profile', page=pagination.prev_num, filter=filter) }}">上一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if pagination.page == page_num %}
                                    <li class="page-item active" aria-current="page">
                                        <a class="page-link" href="{{ url_for('profile', page=page_num, filter=filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('profile', page=page_num, filter=filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('profile', page=pagination.next_num, filter=filter) }}">下一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                        <h5>暂无做题记录</h5>
                        <p class="text-muted">开始刷题，记录将会显示在这里</p>
                        <a href="{{ url_for('problems') }}" class="btn btn-primary mt-2">
                            <i class="fas fa-code me-1"></i>浏览题库
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        width: 80px;
        height: 80px;
        background-color: #4361ee;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .avatar-text {
        color: white;
        font-size: 32px;
        font-weight: bold;
    }
    
    .user-stats {
        margin-bottom: 20px;
    }
    
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #4361ee;
    }
    
    .stat-label {
        font-size: 12px;
        color: #6c757d;
    }
    
    .user-info .info-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .user-info .info-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}
