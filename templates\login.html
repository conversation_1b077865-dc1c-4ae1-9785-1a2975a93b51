{% extends "base.html" %}

{% block title %}登录 - 智能刷题平台{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-5">
        <div class="card shadow-lg border-0 rounded-lg mt-4">
            <div class="card-header bg-primary text-white text-center py-4">
                <h3 class="my-2"><i class="fas fa-sign-in-alt me-2"></i>用户登录</h3>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <p class="lead text-muted">登录您的账号，开始智能刷题之旅</p>
                </div>
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2 text-primary"></i>用户名
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-user text-primary"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="请输入您的用户名" required>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2 text-primary"></i>密码
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-lock text-primary"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="请输入您的密码" required>
                        </div>
                    </div>
                    <div class="mb-4 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">记住我</label>
                        </div>
                        <a href="#" class="text-decoration-none small">忘记密码？</a>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary py-2">
                            <i class="fas fa-sign-in-alt me-2"></i>登录
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    没有账号？
                    <a href="{{ url_for('register') }}" class="text-decoration-none">
                        <i class="fas fa-user-plus me-1"></i>立即注册
                    </a>
                </div>
            </div>
        </div>

        <!-- 测试账号信息 -->
        <div class="alert alert-info mt-4">
            <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>测试账号</h5>
            <p>您可以使用以下账号进行测试：</p>
            <ul class="mb-0">
                <li><strong>用户名：</strong> test</li>
                <li><strong>密码：</strong> test123</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
