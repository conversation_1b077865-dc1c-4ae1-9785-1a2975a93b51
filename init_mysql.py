import pymysql
import os
from app import app, db, User, Problem, UserRecord, UserRegistrationInfo, LearningPath, LearningPathStep
from werkzeug.security import generate_password_hash
from datetime import datetime

def create_database():
    """创建MySQL数据库"""
    try:
        # 连接MySQL服务器
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )

        # 创建游标
        cursor = conn.cursor()

        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS problem_site CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")

        print("数据库创建成功！")

        # 关闭连接
        cursor.close()
        conn.close()

        return True
    except Exception as e:
        print(f"创建数据库出错: {str(e)}")
        return False

def init_database():
    """初始化数据库表和示例数据"""
    with app.app_context():
        # 创建所有表
        db.create_all()

        # 检查是否已有用户
        if User.query.count() > 0:
            print("数据库已初始化，跳过示例数据创建")
            return

        # 创建示例用户
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            password=generate_password_hash('admin123'),
            date_registered=datetime.now(),
            is_active=True
        )

        test_user = User(
            username='test',
            email='<EMAIL>',
            password=generate_password_hash('test123'),
            date_registered=datetime.now(),
            is_active=True
        )

        db.session.add(admin_user)
        db.session.add(test_user)
        db.session.flush()  # 获取用户ID

        # 创建用户注册信息
        admin_reg_info = UserRegistrationInfo(
            user_id=admin_user.id,
            ip_address='127.0.0.1',
            user_agent='Example User Agent',
            registration_date=datetime.now()
        )

        test_reg_info = UserRegistrationInfo(
            user_id=test_user.id,
            ip_address='127.0.0.1',
            user_agent='Example User Agent',
            registration_date=datetime.now()
        )

        db.session.add(admin_reg_info)
        db.session.add(test_reg_info)

        # 创建示例题目
        problems = [
            Problem(
                title='两数之和',
                description='''
                <p>给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值 target 的那两个整数，并返回它们的数组下标。</p>
                <p>你可以假设每种输入只会对应一个答案。但是，数组中同一个元素在答案里不能重复出现。</p>
                <p>你可以按任意顺序返回答案。</p>

                <p><strong>示例 1：</strong></p>
                <pre>
                输入：nums = [2,7,11,15], target = 9
                输出：[0,1]
                解释：因为 nums[0] + nums[1] == 9 ，返回 [0, 1] 。
                </pre>

                <p><strong>示例 2：</strong></p>
                <pre>
                输入：nums = [3,2,4], target = 6
                输出：[1,2]
                </pre>

                <p><strong>示例 3：</strong></p>
                <pre>
                输入：nums = [3,3], target = 6
                输出：[0,1]
                </pre>
                ''',
                difficulty='简单',
                category='算法',
                date_added=datetime.now()
            ),
            Problem(
                title='合并两个有序链表',
                description='''
                <p>将两个升序链表合并为一个新的 <strong>升序</strong> 链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。</p>

                <p><strong>示例 1：</strong></p>
                <pre>
                输入：l1 = [1,2,4], l2 = [1,3,4]
                输出：[1,1,2,3,4,4]
                </pre>

                <p><strong>示例 2：</strong></p>
                <pre>
                输入：l1 = [], l2 = []
                输出：[]
                </pre>

                <p><strong>示例 3：</strong></p>
                <pre>
                输入：l1 = [], l2 = [0]
                输出：[0]
                </pre>
                ''',
                difficulty='简单',
                category='数据结构',
                date_added=datetime.now()
            ),
            Problem(
                title='最长回文子串',
                description='''
                <p>给你一个字符串 s，找到 s 中最长的回文子串。</p>
                <p>如果字符串的反序与原始字符串相同，则该字符串称为回文字符串。</p>

                <p><strong>示例 1：</strong></p>
                <pre>
                输入：s = "babad"
                输出："bab"
                解释："aba" 同样是符合题意的答案。
                </pre>

                <p><strong>示例 2：</strong></p>
                <pre>
                输入：s = "cbbd"
                输出："bb"
                </pre>
                ''',
                difficulty='中等',
                category='算法',
                date_added=datetime.now()
            ),
        ]

        for problem in problems:
            db.session.add(problem)

        # 创建一些用户做题记录
        records = [
            UserRecord(
                user_id=1,
                problem_id=1,
                status='solved',
                date_attempted=datetime.now()
            ),
            UserRecord(
                user_id=1,
                problem_id=2,
                status='solved',
                date_attempted=datetime.now()
            ),
            UserRecord(
                user_id=2,
                problem_id=1,
                status='attempted',
                date_attempted=datetime.now()
            )
        ]

        for record in records:
            db.session.add(record)

        # 创建示例学习路径
        learning_paths = [
            LearningPath(
                title='算法入门之旅',
                description='适合初学者的算法学习路径，从基础概念开始，逐步掌握常见算法和数据结构。',
                difficulty='beginner',
                category='算法',
                created_at=datetime.now()
            ),
            LearningPath(
                title='数据结构进阶',
                description='深入学习各种数据结构及其应用，提升解决复杂问题的能力。',
                difficulty='intermediate',
                category='数据结构',
                created_at=datetime.now()
            )
        ]

        for path in learning_paths:
            db.session.add(path)

        db.session.flush()  # 获取学习路径ID

        # 为第一个学习路径添加步骤
        path1_steps = [
            LearningPathStep(
                learning_path_id=1,
                title='算法基础概念',
                description='了解算法的定义、时间复杂度和空间复杂度等基本概念。',
                order=1,
                content='<h4>什么是算法？</h4><p>算法是解决问题的明确步骤序列。一个好的算法应该具有以下特点：</p><ul><li>正确性：能够正确解决问题</li><li>效率性：使用尽可能少的资源（时间和空间）</li><li>可读性：易于理解和实现</li></ul><h4>时间复杂度</h4><p>时间复杂度用大O表示法描述算法的运行时间如何随着输入规模的增长而增长。常见的时间复杂度有：</p><ul><li>O(1)：常数时间</li><li>O(log n)：对数时间</li><li>O(n)：线性时间</li><li>O(n log n)：线性对数时间</li><li>O(n²)：平方时间</li><li>O(2^n)：指数时间</li></ul>'
            ),
            LearningPathStep(
                learning_path_id=1,
                title='数组与链表',
                description='学习两种最基本的数据结构：数组和链表，以及它们的操作和应用。',
                order=2,
                content='<h4>数组</h4><p>数组是最基本的数据结构之一，它将元素存储在连续的内存位置。</p><h5>特点：</h5><ul><li>随机访问：O(1)</li><li>插入/删除：O(n)</li><li>固定大小（在某些语言中）</li></ul><h4>链表</h4><p>链表是由节点组成的线性集合，每个节点包含数据和指向下一个节点的引用。</p><h5>特点：</h5><ul><li>随机访问：O(n)</li><li>插入/删除：O(1)（如果已知位置）</li><li>动态大小</li></ul>',
                problem_id=2  # 关联"合并两个有序链表"题目
            ),
            LearningPathStep(
                learning_path_id=1,
                title='排序算法',
                description='学习常见的排序算法，如冒泡排序、选择排序、插入排序、快速排序等。',
                order=3,
                content='<h4>排序算法</h4><p>排序算法用于将一组数据按照特定顺序排列。常见的排序算法包括：</p><h5>1. 冒泡排序</h5><p>时间复杂度：O(n²)</p><pre>function bubbleSort(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        for (let j = 0; j < arr.length - i - 1; j++) {\n            if (arr[j] > arr[j + 1]) {\n                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];\n            }\n        }\n    }\n    return arr;\n}</pre><h5>2. 快速排序</h5><p>时间复杂度：平均 O(n log n)，最坏 O(n²)</p><pre>function quickSort(arr) {\n    if (arr.length <= 1) return arr;\n    \n    const pivot = arr[Math.floor(arr.length / 2)];\n    const left = arr.filter(x => x < pivot);\n    const middle = arr.filter(x => x === pivot);\n    const right = arr.filter(x => x > pivot);\n    \n    return [...quickSort(left), ...middle, ...quickSort(right)];\n}</pre>'
            ),
            LearningPathStep(
                learning_path_id=1,
                title='搜索算法',
                description='学习常见的搜索算法，如线性搜索、二分搜索等。',
                order=4,
                content='<h4>搜索算法</h4><p>搜索算法用于在数据集中查找特定元素。常见的搜索算法包括：</p><h5>1. 线性搜索</h5><p>时间复杂度：O(n)</p><pre>function linearSearch(arr, target) {\n    for (let i = 0; i < arr.length; i++) {\n        if (arr[i] === target) return i;\n    }\n    return -1;\n}</pre><h5>2. 二分搜索</h5><p>时间复杂度：O(log n)（要求数组已排序）</p><pre>function binarySearch(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left <= right) {\n        const mid = Math.floor((left + right) / 2);\n        if (arr[mid] === target) return mid;\n        if (arr[mid] < target) left = mid + 1;\n        else right = mid - 1;\n    }\n    \n    return -1;\n}</pre>'
            ),
            LearningPathStep(
                learning_path_id=1,
                title='实战练习：两数之和',
                description='通过解决"两数之和"问题，综合应用所学知识。',
                order=5,
                problem_id=1  # 关联"两数之和"题目
            )
        ]

        # 为第二个学习路径添加步骤
        path2_steps = [
            LearningPathStep(
                learning_path_id=2,
                title='栈与队列',
                description='深入理解栈和队列数据结构及其应用场景。',
                order=1,
                content='<h4>栈</h4><p>栈是一种遵循后进先出（LIFO）原则的数据结构。</p><h5>主要操作：</h5><ul><li>push：将元素添加到栈顶</li><li>pop：移除栈顶元素</li><li>peek：查看栈顶元素但不移除</li></ul><h5>应用场景：</h5><ul><li>函数调用栈</li><li>表达式求值</li><li>括号匹配</li></ul><h4>队列</h4><p>队列是一种遵循先进先出（FIFO）原则的数据结构。</p><h5>主要操作：</h5><ul><li>enqueue：将元素添加到队尾</li><li>dequeue：移除队首元素</li><li>peek：查看队首元素但不移除</li></ul><h5>应用场景：</h5><ul><li>任务调度</li><li>广度优先搜索</li><li>缓冲区管理</li></ul>'
            ),
            LearningPathStep(
                learning_path_id=2,
                title='树与图',
                description='学习树和图数据结构，以及相关的遍历和搜索算法。',
                order=2,
                content='<h4>树</h4><p>树是一种非线性数据结构，由节点和边组成，没有环路。</p><h5>常见类型：</h5><ul><li>二叉树：每个节点最多有两个子节点</li><li>二叉搜索树：左子树的所有节点值小于根节点，右子树的所有节点值大于根节点</li><li>平衡树：如AVL树、红黑树等</li></ul><h5>遍历方式：</h5><ul><li>前序遍历：根-左-右</li><li>中序遍历：左-根-右</li><li>后序遍历：左-右-根</li><li>层序遍历：按层从左到右</li></ul><h4>图</h4><p>图是由顶点和边组成的数据结构，可以表示复杂的关系。</p><h5>表示方法：</h5><ul><li>邻接矩阵</li><li>邻接表</li></ul><h5>遍历算法：</h5><ul><li>深度优先搜索（DFS）</li><li>广度优先搜索（BFS）</li></ul>'
            ),
            LearningPathStep(
                learning_path_id=2,
                title='哈希表',
                description='深入理解哈希表的原理、实现和应用。',
                order=3,
                content='<h4>哈希表</h4><p>哈希表是一种使用哈希函数将键映射到值的数据结构，提供快速的插入和查找操作。</p><h5>主要组成：</h5><ul><li>哈希函数：将键转换为数组索引</li><li>冲突解决：处理不同键映射到相同索引的情况</li></ul><h5>冲突解决方法：</h5><ul><li>链地址法：在每个索引位置维护一个链表</li><li>开放寻址法：如线性探测、二次探测等</li></ul><h5>性能：</h5><ul><li>平均情况：O(1)的插入、删除和查找</li><li>最坏情况：O(n)（当所有键都映射到同一个索引时）</li></ul><h5>应用场景：</h5><ul><li>数据库索引</li><li>缓存实现</li><li>集合和字典数据结构</li></ul>'
            ),
            LearningPathStep(
                learning_path_id=2,
                title='实战练习：最长回文子串',
                description='通过解决"最长回文子串"问题，综合应用所学的数据结构知识。',
                order=4,
                problem_id=3  # 关联"最长回文子串"题目
            )
        ]

        # 添加所有步骤
        for step in path1_steps + path2_steps:
            db.session.add(step)

        # 提交所有更改
        db.session.commit()

        print("数据库初始化完成！")

if __name__ == "__main__":
    # 先创建数据库
    if create_database():
        # 重新加载 app.py 或重启脚本，确保 SQLAlchemy 能连接到已存在的数据库
        init_database()
    else:
        print("数据库创建失败，请检查MySQL连接配置")
