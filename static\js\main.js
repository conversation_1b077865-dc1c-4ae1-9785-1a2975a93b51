// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 激活当前页面的导航链接
    activateCurrentNavLink();
    
    // 设置表单验证
    setupFormValidation();
});

// 激活当前页面的导航链接
function activateCurrentNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath) {
            link.classList.add('active');
        }
    });
}

// 设置表单验证
function setupFormValidation() {
    const registerForm = document.querySelector('form[action="/register"]');
    if (registerForm) {
        registerForm.addEventListener('submit', function(event) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                event.preventDefault();
                alert('两次输入的密码不匹配');
            }
        });
    }
}
