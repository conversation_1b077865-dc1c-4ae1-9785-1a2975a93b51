{% extends "base.html" %}

{% block title %}{{ path.title }} - 学习路径 - 智能刷题平台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- 左侧：路径信息 -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-road me-2"></i>路径信息</h4>
                </div>
                <div class="card-body">
                    <h3 class="card-title">{{ path.title }}</h3>
                    <div class="mb-3">
                        <span class="badge {% if path.difficulty == 'beginner' %}bg-success{% elif path.difficulty == 'intermediate' %}bg-warning{% else %}bg-danger{% endif %} me-2">
                            {% if path.difficulty == 'beginner' %}
                            初级
                            {% elif path.difficulty == 'intermediate' %}
                            中级
                            {% else %}
                            高级
                            {% endif %}
                        </span>
                        <span class="badge bg-secondary">{{ path.category }}</span>
                    </div>
                    <p class="card-text">{{ path.description }}</p>
                    
                    <div class="d-grid gap-2 mt-4">
                        {% if current_user.is_authenticated %}
                            {% if enrollment %}
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ (enrollment.current_step / path.steps|length * 100)|round }}%;" 
                                         aria-valuenow="{{ (enrollment.current_step / path.steps|length * 100)|round }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                                <div class="text-center text-muted small mb-3">
                                    进度: {{ enrollment.current_step }}/{{ path.steps|length }} 步骤
                                </div>
                                
                                {% if enrollment.completed %}
                                <button class="btn btn-success" disabled>
                                    <i class="fas fa-check-circle me-1"></i>已完成学习
                                </button>
                                <div class="text-center text-muted small mt-2">
                                    完成时间: {{ enrollment.completed_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                                {% else %}
                                <a href="#step-{{ enrollment.current_step }}" class="btn btn-primary">
                                    <i class="fas fa-play me-1"></i>继续学习
                                </a>
                                {% endif %}
                            {% else %}
                                <form action="{{ url_for('enroll_path', path_id=path.id) }}" method="post">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-user-plus me-1"></i>注册学习路径
                                    </button>
                                </form>
                            {% endif %}
                        {% else %}
                            <a href="{{ url_for('login') }}" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>登录后注册学习路径
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-tasks me-1"></i>{{ path.steps|length }} 个步骤</span>
                        <span><i class="fas fa-calendar-alt me-1"></i>{{ path.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：学习步骤 -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-list-ol me-2"></i>学习步骤</h4>
                </div>
                <div class="card-body">
                    {% if path.steps %}
                    <div class="learning-steps">
                        {% for step in path.steps %}
                        <div id="step-{{ step.order }}" class="learning-step mb-4 {% if enrollment and enrollment.current_step > step.order %}completed{% elif enrollment and enrollment.current_step == step.order %}current{% else %}locked{% endif %}">
                            <div class="step-header d-flex align-items-center">
                                <div class="step-number me-3">
                                    <span>{{ step.order }}</span>
                                </div>
                                <h4 class="mb-0">{{ step.title }}</h4>
                            </div>
                            
                            <div class="step-content mt-3 ps-5">
                                <p>{{ step.description }}</p>
                                
                                {% if step.content %}
                                <div class="step-learning-content mb-3">
                                    {{ step.content|safe }}
                                </div>
                                {% endif %}
                                
                                {% if step.problem_id %}
                                <div class="step-problem mb-3">
                                    <h5><i class="fas fa-code me-2 text-primary"></i>相关题目</h5>
                                    <a href="{{ url_for('problem_detail', problem_id=step.problem_id) }}" class="btn btn-outline-primary">
                                        <i class="fas fa-external-link-alt me-1"></i>查看题目
                                    </a>
                                </div>
                                {% endif %}
                                
                                {% if enrollment and enrollment.current_step == step.order and not enrollment.completed %}
                                <div class="step-actions mt-3">
                                    <form action="{{ url_for('update_path_progress', path_id=path.id, step=step.order+1) }}" method="post">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-check me-1"></i>完成此步骤
                                        </button>
                                    </form>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        此学习路径暂无步骤内容
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .learning-step {
        position: relative;
        padding-bottom: 20px;
    }
    
    .learning-step:not(:last-child)::before {
        content: '';
        position: absolute;
        top: 30px;
        left: 15px;
        height: calc(100% - 30px);
        width: 2px;
        background-color: #dee2e6;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #495057;
    }
    
    .learning-step.completed .step-number {
        background-color: #28a745;
        color: white;
    }
    
    .learning-step.current .step-number {
        background-color: #007bff;
        color: white;
    }
    
    .learning-step.locked .step-content {
        opacity: 0.7;
    }
    
    .step-learning-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border-left: 3px solid #007bff;
    }
</style>
{% endblock %}
