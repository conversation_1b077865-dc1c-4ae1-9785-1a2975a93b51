import os
import requests
import json
import re
import random

# DeepSeek API密钥
DEEPSEEK_API_KEY = "***********************************"

class AIService:
    """AI服务类，提供与大模型交互的功能"""

    @staticmethod
    def call_deepseek_api(prompt, temperature=0.7, max_tokens=1000):
        """
        调用DeepSeek API

        Args:
            prompt: 提示词
            temperature: 温度参数
            max_tokens: 最大生成token数

        Returns:
            生成的文本
        """
        try:
            url = "https://api.deepseek.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
                "Content-Type": "application/json"
            }
            data = {
                "model": "deepseek-chat",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            # 增加重试机制
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    response = requests.post(url, headers=headers, json=data, timeout=60)
                    response.raise_for_status()
                    return response.json()["choices"][0]["message"]["content"]
                except requests.exceptions.RequestException as e:
                    retry_count += 1
                    print(f"API调用失败，尝试重试 ({retry_count}/{max_retries}): {str(e)}")
                    if retry_count >= max_retries:
                        raise
                    # 指数退避策略
                    import time
                    time.sleep(2 ** retry_count)

            # 如果所有重试都失败
            raise Exception("所有重试都失败")

        except Exception as e:
            print(f"调用DeepSeek API出错: {str(e)}")
            print("用的是假数据，prompt内容：", prompt)
            # 返回模拟数据，而不是错误信息
            return AIService.get_mock_response(prompt)

    @staticmethod
    def get_mock_response(prompt):
        """
        生成模拟响应，在API调用失败时使用

        Args:
            prompt: 提示词

        Returns:
            模拟生成的文本
        """
        # 根据提示词中的关键词判断请求类型
        if "搜索" in prompt or "匹配" in prompt:
            return "[1, 2, 3]"  # 模拟搜索结果
        elif "知识点" in prompt:
            return """
# 核心知识点

这道题目主要涉及以下知识点：

1. **数据结构**：数组、哈希表
2. **算法思想**：双指针、哈希映射

## 解题思路

1. 使用哈希表存储每个元素及其索引
2. 遍历数组，检查目标值与当前元素的差是否在哈希表中

## 代码实现要点

```python
def solution(nums, target):
    hash_map = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in hash_map:
            return [hash_map[complement], i]
        hash_map[num] = i
    return []
```

## 时间复杂度分析

- 时间复杂度：O(n)，其中n是数组长度
- 空间复杂度：O(n)，需要哈希表存储元素
            """
        elif "视频" in prompt:
            return """
1. 标题: 算法基础 - 两数之和详解
   描述: 详细讲解两数之和问题的多种解法及优化思路
   链接: https://www.bilibili.com/video/BV1GJ411x7h7

2. 标题: 哈希表在算法中的应用
   描述: 通过两数之和等经典问题讲解哈希表的使用技巧
   链接: https://www.bilibili.com/video/BV1Wf4y1U7EL

3. 标题: LeetCode刷题指南 - 从两数之和开始
   描述: 零基础入门算法，从最简单的两数之和问题开始
   链接: https://www.bilibili.com/video/BV1nt4y1Y7nz
            """
        else:
            return "由于网络连接问题，无法获取AI响应。请稍后再试。"

    @staticmethod
    def search_problems(query, problems):
        """
        使用AI搜索题目，返回题目完整信息

        Args:
            query: 用户查询字符串
            problems: 题目列表

        Returns:
            匹配的题目完整信息列表（dict）
        """
        # 首先尝试使用关键词匹配，作为备选结果
        keyword_matched = []
        for problem in problems:
            if (query.lower() in problem.title.lower() or
                query.lower() in problem.description.lower() or
                query.lower() in problem.category.lower() or
                query.lower() in problem.difficulty.lower()):
                keyword_matched.append(problem)

        # 如果关键词匹配找到了结果，就不需要调用API
        if keyword_matched:
            print(f"关键词匹配找到了 {len(keyword_matched)} 个结果")
            # 返回题目完整信息
            return [
                {
                    "id": p.id,
                    "title": p.title,
                    "description": p.description,
                    "difficulty": p.difficulty,
                    "category": p.category
                } for p in keyword_matched
            ]

        try:
            # 构建提示
            prompt = f"""
            用户正在搜索: "{query}"
            请帮我生成3道相关的编程题目，每道题目包含：标题、描述、难度（简单/中等/困难）、分类。
            返回JSON数组，例如：
            [
              {{"title": "题目1", "description": "描述1", "difficulty": "简单", "category": "算法"}},
              ...
            ]
            """
            # 调用DeepSeek API
            content = AIService.call_deepseek_api(prompt, temperature=0.3, max_tokens=500)
            # 提取JSON数组
            match = re.search(r'\[.*\]', content, re.DOTALL)
            if match:
                content = match.group(0)
                try:
                    problem_list = json.loads(content)
                    # 返回题目完整信息
                    return problem_list
                except Exception as e:
                    print("AI返回内容解析失败，使用mock数据", e)
            # 如果AI返回内容不对，使用mock数据
            raise Exception("AI返回内容不对")
        except Exception as e:
            print(f"AI搜索题目失败: {str(e)}，用mock数据演示")
            # mock三道题
            return [
                {"title": "两数之和", "description": "给定一个整数数组和目标值，找出和为目标值的两个数的下标。", "difficulty": "简单", "category": "算法"},
                {"title": "二叉树的最大深度", "description": "给定一棵二叉树，求最大深度。", "difficulty": "中等", "category": "数据结构"},
                {"title": "LRU缓存机制", "description": "设计和实现一个LRU缓存。", "difficulty": "困难", "category": "设计"}
            ]

    @staticmethod
    def generate_knowledge_points(problem):
        """
        为题目生成知识点

        Args:
            problem: 题目对象

        Returns:
            生成的知识点文本
        """
        # 构建提示
        prompt = f"""
        请为以下编程题目生成详细的知识点解析：

        题目标题: {problem.title}
        题目难度: {problem.difficulty}
        题目分类: {problem.category}
        题目描述:
        {problem.description}

        请提供以下内容:
        1. 这道题目涉及的核心知识点
        2. 解题思路和常用算法
        3. 相关的数据结构
        4. 常见的解题陷阱和注意事项
        5. 扩展知识和相关题目推荐

        请使用markdown格式，清晰地组织内容。
        """

        # 调用DeepSeek API
        return AIService.call_deepseek_api(prompt, temperature=0.7, max_tokens=1000)

    @staticmethod
    def search_videos(problem):
        """
        搜索与题目相关的视频解析

        Args:
            problem: 题目对象

        Returns:
            视频链接列表（逗号分隔的字符串）
        """
        # 构建提示
        prompt = f"""
        请为以下编程题目推荐3个教学视频的标题和简短描述：

        题目标题: {problem.title}
        题目难度: {problem.difficulty}
        题目分类: {problem.category}

        请为每个视频提供一个标题和简短描述，格式如下:
        1. 标题: [视频标题]
           描述: [简短描述]
           链接: https://www.bilibili.com/video/[视频ID]

        2. 标题: [视频标题]
           描述: [简短描述]
           链接: https://www.bilibili.com/video/[视频ID]

        3. 标题: [视频标题]
           描述: [简短描述]
           链接: https://www.bilibili.com/video/[视频ID]

        只需提供这些信息，不需要其他解释。
        """

        # 调用DeepSeek API
        return AIService.call_deepseek_api(prompt, temperature=0.7, max_tokens=500)

    @staticmethod
    def generate_answer(problem):
        """
        调用AI生成题目标准答案
        Args:
            problem: dict，包含title、description、difficulty、category
        Returns:
            答案字符串
        """
        try:
            prompt = f"""
            请为以下编程题目生成标准答案（包括代码和简要解析）：\n\n题目标题: {problem['title']}\n题目难度: {problem['difficulty']}\n题目分类: {problem['category']}\n题目描述: {problem['description']}\n\n请用markdown格式输出，代码用代码块包裹。
            """
            answer = AIService.call_deepseek_api(prompt, temperature=0.2, max_tokens=800)
            return answer
        except Exception as e:
            print(f"生成答案失败: {str(e)}")
            return "AI生成答案失败，请稍后重试。"
