{% extends "base.html" %}

{% block title %}题库 - 刷题网站{% endblock %}

{% block content %}
<h1 class="mb-4">题库</h1>

<div class="row mb-4">
    <div class="col-md-8">
        <form action="{{ url_for('problems') }}" method="get" class="d-flex">
            <input class="form-control me-2" type="search" placeholder="搜索题目" name="search" value="{{ request.args.get('search', '') }}">
            <button class="btn btn-outline-primary" type="submit">搜索</button>
        </form>
    </div>
    <div class="col-md-4">
        <div class="d-flex justify-content-end">
            <div class="dropdown me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    分类
                </button>
                <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
                    <li><a class="dropdown-item" href="{{ url_for('problems') }}">全部</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', category='算法') }}">算法</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', category='数据结构') }}">数据结构</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', category='数学') }}">数学</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', category='数据库') }}">数据库</a></li>
                </ul>
            </div>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="difficultyDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    难度
                </button>
                <ul class="dropdown-menu" aria-labelledby="difficultyDropdown">
                    <li><a class="dropdown-item" href="{{ url_for('problems') }}">全部</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', difficulty='简单') }}">简单</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', difficulty='中等') }}">中等</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('problems', difficulty='困难') }}">困难</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if problems.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>标题</th>
                        <th>分类</th>
                        <th>难度</th>
                        <th>添加时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for problem in problems.items %}
                    <tr>
                        <td>{{ problem.id }}</td>
                        <td><a href="{{ url_for('problem_detail', problem_id=problem.id) }}">{{ problem.title }}</a></td>
                        <td>{{ problem.category }}</td>
                        <td>
                            {% if problem.difficulty == '简单' %}
                            <span class="badge bg-success">简单</span>
                            {% elif problem.difficulty == '中等' %}
                            <span class="badge bg-warning">中等</span>
                            {% else %}
                            <span class="badge bg-danger">困难</span>
                            {% endif %}
                        </td>
                        <td>{{ problem.date_added.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if problems.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('problems', page=problems.prev_num, category=request.args.get('category'), difficulty=request.args.get('difficulty'), search=request.args.get('search')) }}">上一页</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in problems.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                    {% if page_num %}
                        {% if problems.page == page_num %}
                        <li class="page-item active" aria-current="page">
                            <a class="page-link" href="{{ url_for('problems', page=page_num, category=request.args.get('category'), difficulty=request.args.get('difficulty'), search=request.args.get('search')) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('problems', page=page_num, category=request.args.get('category'), difficulty=request.args.get('difficulty'), search=request.args.get('search')) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if problems.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('problems', page=problems.next_num, category=request.args.get('category'), difficulty=request.args.get('difficulty'), search=request.args.get('search')) }}">下一页</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% else %}
        <div class="alert alert-info">
            没有找到符合条件的题目
        </div>
        {% endif %}
    </div>
</div>

<div class="mt-4">
    <h3>使用AI搜索题目</h3>
    <form action="{{ url_for('ai_search') }}" method="post" class="d-flex">
        <input class="form-control me-2" type="text" placeholder="输入自然语言描述搜索题目" name="query" required>
        <button class="btn btn-primary" type="submit">AI搜索</button>
    </form>
    <small class="text-muted">例如：搜索"二叉树遍历的简单题目"或"与数据库索引相关的题目"</small>
</div>
{% endblock %}
