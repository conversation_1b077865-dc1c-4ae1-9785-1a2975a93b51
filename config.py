import os
from datetime import timedelta

class Config:
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)  # 会话持续7天
    SESSION_TYPE = 'filesystem'  # 使用文件系统存储会话
    SESSION_PERMANENT = True
    SESSION_USE_SIGNER = True
    SESSION_FILE_DIR = os.path.join(os.getcwd(), 'flask_session')  # 会话文件存储路径

    # 数据库配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    DEBUG = True
    # SQLite配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///site.db'

class MySQLConfig(Config):
    # MySQL配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = os.environ.get('MYSQL_PORT') or '3306'
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_DB = os.environ.get('MYSQL_DB') or 'problem_site'

    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:123456@localhost/problem_site'

# 默认配置
config = {
    'development': DevelopmentConfig,
    'mysql': MySQLConfig,
    'default': DevelopmentConfig
}
